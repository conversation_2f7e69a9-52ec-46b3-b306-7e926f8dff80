import { Response } from 'express';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../types/index';
import { DeliveryService } from '../services/DeliveryService';

export class DeliveryController {
  /**
   * Calculate delivery fee for a single supplier
   */
  static async calculateDeliveryFee(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const { supplierId, deliveryAddress, orderTotal } = req.body;

      // Calculate delivery fee
      const calculation = await DeliveryService.calculateDeliveryFee(
        supplierId,
        deliveryAddress,
        orderTotal
      );

      res.json({
        success: true,
        data: {
          deliveryFee: calculation.deliveryFee,
          estimatedTime: calculation.estimatedTime,
          distance: calculation.distance,
          zone: calculation.zone
        }
      });
    } catch (error) {
      console.error('Error calculating delivery fee:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to calculate delivery fee',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  /**
   * Calculate delivery fees for multiple suppliers
   */
  static async calculateMultipleDeliveryFees(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const { supplierIds, deliveryAddress, orderTotals } = req.body;

      // Calculate delivery fees for all suppliers
      const calculations = await DeliveryService.calculateMultipleDeliveryFees(
        supplierIds,
        deliveryAddress,
        orderTotals
      );

      res.json({
        success: true,
        data: calculations
      });
    } catch (error) {
      console.error('Error calculating multiple delivery fees:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to calculate delivery fees',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
