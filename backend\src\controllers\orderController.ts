import { Request, Response } from 'express';
import { Order } from '../models/Order';
import { validationResult } from 'express-validator';

// Use the AuthenticatedRequest from types
import { AuthenticatedRequest } from '../types/index';

// Import security services
import { ProductService } from '../services/ProductService';
import { DeliveryService } from '../services/DeliveryService';
import { PromoCodeService } from '../services/PromoCodeService';
import { SupplierService } from '../services/SupplierService';

export class OrderController {
  // Create a new order - SECURE IMPLEMENTATION
  static async createOrder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const {
        supplierId,
        items,
        paymentMethod,
        deliveryAddress,
        customerPhone,
        notes,
        promoCode
      } = req.body;

      // 🔒 SECURITY STEP 1: Validate supplier
      const supplierValidation = await SupplierService.validateSupplier(supplierId);
      if (!supplierValidation.isValid) {
        res.status(400).json({
          success: false,
          message: supplierValidation.error || 'Invalid supplier'
        });
        return;
      }

      // 🔒 SECURITY STEP 2: Validate delivery address
      if (!DeliveryService.validateDeliveryAddress(deliveryAddress)) {
        res.status(400).json({
          success: false,
          message: 'Invalid delivery address'
        });
        return;
      }

      // 🔒 SECURITY STEP 3: Calculate prices from database (NEVER trust client)
      const priceCalculation = await ProductService.calculateOrderSubtotal(items, supplierId);
      const calculatedSubtotal = priceCalculation.subtotal;

      // 🔒 SECURITY STEP 4: Calculate delivery fee from backend
      const deliveryCalculation = await DeliveryService.calculateDeliveryFee(
        supplierId,
        deliveryAddress,
        calculatedSubtotal
      );
      const calculatedDeliveryFee = deliveryCalculation.deliveryFee;

      // 🔒 SECURITY STEP 5: Validate and calculate promo discount
      let calculatedPromoDiscount = 0;
      let validatedPromoCode = null;
      if (promoCode) {
        const promoValidation = await PromoCodeService.validatePromoCode(
          promoCode,
          calculatedSubtotal,
          userId
        );

        if (promoValidation.isValid) {
          calculatedPromoDiscount = promoValidation.discountAmount;
          validatedPromoCode = promoCode;
        }
        // Note: We don't fail the order if promo code is invalid, just ignore it
      }

      // 🔒 SECURITY STEP 6: Calculate final total (BACKEND CALCULATED ONLY)
      const calculatedTotalAmount = calculatedSubtotal + calculatedDeliveryFee - calculatedPromoDiscount;

      // Generate unique order ID
      const orderId = `ORD-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Create order with BACKEND-CALCULATED values only
      const order = new Order({
        orderId,
        userId,
        supplierId,
        supplierName: supplierValidation.supplier.name,
        items: items.map((item: any, index: number) => ({
          productId: item.productId,
          productName: item.productName || 'Product',
          productImage: item.productImage || '',
          quantity: item.quantity,
          price: priceCalculation.itemCalculations[index]?.finalPrice || 0,
          selectedOptions: {
            size: item.options?.selectedSize || '',
            color: item.options?.selectedColor || '',
            additions: priceCalculation.itemCalculations[index]?.calculatedAdditions || [],
            without: item.options?.without || [],
            sides: priceCalculation.itemCalculations[index]?.calculatedSides || []
          },
          subtotal: priceCalculation.itemCalculations[index]?.subtotal || 0
        })),
        subtotal: calculatedSubtotal,
        deliveryFee: calculatedDeliveryFee,
        totalAmount: calculatedTotalAmount,
        paymentMethod,
        deliveryAddress,
        customerPhone,
        notes,
        promoCode: validatedPromoCode,
        promoDiscount: calculatedPromoDiscount,
        status: 'pending',
        paymentStatus: 'pending',
        estimatedDeliveryTime: deliveryCalculation.estimatedTime
      });

      await order.save();

      res.status(201).json({
        success: true,
        message: 'Order created successfully',
        data: {
          orderId: order.orderId,
          subtotal: calculatedSubtotal,
          deliveryFee: calculatedDeliveryFee,
          promoDiscount: calculatedPromoDiscount,
          totalAmount: calculatedTotalAmount,
          estimatedDeliveryTime: deliveryCalculation.estimatedTime,
          status: order.status,
          createdAt: order.createdAt
        }
      });
    } catch (error) {
      console.error('Error creating order:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create order',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get user's orders
  static async getUserOrders(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const { page = 1, limit = 20, status, search } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = Math.min(parseInt(limit as string), 50); // Max 50 orders per page
      const skip = (pageNum - 1) * limitNum;

      const query: any = { userId };
      if (status) {
        query.status = status;
      }

      // Add search functionality with validation
      if (search) {
        const searchTerm = (search as string).trim();

        // Validate search term
        if (searchTerm.length > 100) {
          res.status(400).json({
            success: false,
            message: 'Search term too long (max 100 characters)'
          });
          return;
        }

        if (searchTerm.length >= 2) { // Minimum 2 characters for search
          // Escape special regex characters for safety
          const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

          query.$or = [
            { orderId: { $regex: escapedTerm, $options: 'i' } },
            { supplierName: { $regex: escapedTerm, $options: 'i' } },
            { 'items.productName': { $regex: escapedTerm, $options: 'i' } },
            { 'deliveryAddress.street': { $regex: escapedTerm, $options: 'i' } },
            { customerPhone: { $regex: escapedTerm, $options: 'i' } }
          ];
        }
      }

      const orders = await Order.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Order.countDocuments(query);

      res.json({
        success: true,
        data: orders,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching user orders:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch orders',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get order by ID
  static async getOrderById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { orderId } = req.params;
      const userId = req.user?._id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      let order;
      if (orderId.length === 24 && /^[0-9a-fA-F]{24}$/.test(orderId)) {
        order = await Order.findOne({ 
          _id: orderId,
          userId 
        }).select('-__v');
      } else {
        order = await Order.findOne({ 
          orderId,
          userId 
        }).select('-__v');
      }

      if (!order) {
        res.status(404).json({
          success: false,
          message: 'Order not found'
        });
        return;
      }

      res.json({
        success: true,
        data: order
      });
    } catch (error) {
      console.error('Error fetching order:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch order',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update order status
  static async updateOrderStatus(req: Request, res: Response): Promise<void> {
    try {
      const { orderId } = req.params;
      const { status, trackingUpdate } = req.body;

      const order = await Order.findOne({ 
        $or: [
          { orderId },
          { _id: orderId }
        ]
      });

      if (!order) {
        res.status(404).json({
          success: false,
          message: 'Order not found'
        });
        return;
      }

      order.status = status;

      if (trackingUpdate) {
        order.trackingUpdates.push({
          status,
          timestamp: new Date(),
          message: trackingUpdate.message,
          location: trackingUpdate.location
        });
      }

      await order.save();

      res.json({
        success: true,
        message: 'Order status updated successfully',
        data: order
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update order status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
