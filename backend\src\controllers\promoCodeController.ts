import { Response } from 'express';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../types/index';
import { PromoCodeService } from '../services/PromoCodeService';

export class PromoCodeController {
  /**
   * Validate promo code and return discount information
   */
  static async validatePromoCode(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const { code, orderTotal } = req.body;

      // Validate promo code
      const validation = await PromoCodeService.validatePromoCode(code, orderTotal, userId);

      if (validation.isValid) {
        res.json({
          success: true,
          data: {
            isValid: true,
            discountAmount: validation.discountAmount,
            discountPercentage: validation.discountPercentage,
            message: validation.message
          }
        });
      } else {
        res.status(400).json({
          success: false,
          data: {
            isValid: false,
            discountAmount: 0,
            message: validation.message
          }
        });
      }
    } catch (error) {
      console.error('Error validating promo code:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate promo code',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
