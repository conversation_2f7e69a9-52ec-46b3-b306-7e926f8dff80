import { Router } from 'express';
import { body } from 'express-validator';
import { DeliveryController } from '../controllers/deliveryController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Validation rules for delivery fee calculation
const calculateDeliveryFeeValidation = [
  body('supplierId')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Supplier ID is required'),
  body('deliveryAddress.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  body('deliveryAddress.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180'),
  body('deliveryAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Street address is required'),
  body('deliveryAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('City is required'),
  body('orderTotal')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Order total must be a positive number')
];

// Validation rules for multiple delivery fees calculation
const calculateMultipleDeliveryFeesValidation = [
  body('supplierIds')
    .isArray({ min: 1 })
    .withMessage('Supplier IDs array is required and must contain at least one supplier'),
  body('supplierIds.*')
    .isString()
    .withMessage('Each supplier ID must be a string'),
  body('deliveryAddress.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  body('deliveryAddress.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180'),
  body('deliveryAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Street address is required'),
  body('deliveryAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('City is required'),
  body('orderTotals')
    .optional()
    .isObject()
    .withMessage('Order totals must be an object')
];

// All routes require authentication
router.use(authenticate);

// Delivery routes
router.post('/calculate-fee', calculateDeliveryFeeValidation, DeliveryController.calculateDeliveryFee);
router.post('/calculate-multiple-fees', calculateMultipleDeliveryFeesValidation, DeliveryController.calculateMultipleDeliveryFees);

export default router;
