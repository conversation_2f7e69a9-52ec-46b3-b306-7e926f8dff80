import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft, Clock, MapPin, Phone, CreditCard, Package, User, Truck, Star, Navigation,
  CheckCircle, AlertCircle, Loader, MessageSquare, RotateCcw, Eye, Calendar,
  Shield, Award, Zap, Heart, ChevronDown, ChevronUp
} from 'lucide-react';
import { useOrdersStore } from '../../stores/ordersStore';
import { apiService } from '../../services/api';
import type { Order } from '../../stores/ordersStore';

const OrderDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { getOrderById, mapBackendStatus } = useOrdersStore();
  const orderId = searchParams.get('orderId');

  // State for animations and interactions
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    items: true,
    delivery: true,
    payment: true
  });

  // Backend integration state
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch order details from backend
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId) {
        setError('No order ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // First try to get from store (cache)
        const cachedOrder = getOrderById(orderId);
        if (cachedOrder) {
          setOrder(cachedOrder);
          setIsLoading(false);
          return;
        }

        // If not in store, fetch from backend
        const response = await apiService.getOrderById(orderId);

        if (response.success && response.data) {
          // Convert backend order to local format
          const backendOrder = response.data;

          // Handle different possible field names from backend
          const orderIdField = backendOrder.orderId || backendOrder._id || backendOrder.id;
          const supplierName = backendOrder.supplierName || backendOrder.supplier?.name || 'Unknown Supplier';
          const supplierId = backendOrder.supplierId || backendOrder.supplier?.id || backendOrder.supplier?._id || '';

          // Handle items array with flexible structure matching backend IOrderItem
          const items = (backendOrder.items || []).map((item: any) => {
            const product = item.product || {};
            const selectedOptions = item.selectedOptions || {};

            return {
              product: {
                id: item.productId || product.id || product._id || '',
                name: item.productName || product.name || item.name || 'Unknown Item',
                image: item.productImage || product.image || item.image || '',
                price: item.price || product.price || 0,
                category: product.category || item.category || '',
              },
              qty: item.quantity || item.qty || 1,
              finalPrice: item.subtotal || item.finalPrice || (item.price * (item.quantity || item.qty || 1)),
              selectedAdditions: selectedOptions.additions || item.selectedAdditions || [],
              selectedSides: selectedOptions.sides || item.selectedSides || [],
              without: selectedOptions.without || item.without || [],
              selectedSize: selectedOptions.size || item.selectedSize || '',
              selectedColor: selectedOptions.color || item.selectedColor || '',
            };
          });

          // Handle delivery address with flexible structure
          const deliveryAddress = backendOrder.deliveryAddress || {};
          const coordinates = deliveryAddress.coordinates || {};

          const formattedOrder: Order = {
            id: orderIdField,
            createdAt: backendOrder.createdAt || backendOrder.placedAt || new Date().toISOString(),
            items,
            supplier: {
              id: supplierId,
              name: supplierName,
            },
            subtotal: backendOrder.subtotal || backendOrder.subTotal || 0,
            deliveryFee: backendOrder.deliveryFee || 5,
            promoDiscount: backendOrder.promoDiscount || backendOrder.discount || 0,
            total: backendOrder.totalAmount || backendOrder.total || 0,
            status: mapBackendStatus(backendOrder.status),
            address: deliveryAddress.street || deliveryAddress.address || backendOrder.address || '',
            phone: backendOrder.customerPhone || backendOrder.phone || '',
            notes: backendOrder.notes || '',
            paymentMethod: (backendOrder.paymentMethod as 'cash' | 'card') || 'cash',
            promoCode: backendOrder.promoCode || '',
            estimatedTime: backendOrder.estimatedDeliveryTime || backendOrder.estimatedTime || '45-60 mins',
            placedAt: backendOrder.createdAt || backendOrder.placedAt || new Date().toISOString(),
            driverName: backendOrder.driverName || '',
            driverPhone: backendOrder.driverPhone || '',
            driverLocation: backendOrder.driverLocation || undefined,
            deliveryLocation: coordinates.lat && coordinates.lng ? {
              lat: coordinates.lat,
              lng: coordinates.lng,
              address: deliveryAddress.street || deliveryAddress.address || '',
            } : undefined,
          };

          setOrder(formattedOrder);
        } else {
          setError(response.message || 'Order not found');
        }
      } catch (err) {
        console.error('Error fetching order details:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch order details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId, getOrderById, mapBackendStatus]);

  // Handle scroll for header animation with smoother debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      // Use requestAnimationFrame for smoother scroll handling
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        // Clear previous timeout
        clearTimeout(timeoutId);

        // Reduced debounce time for more responsive feel
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 120); // Even lower threshold for ultra-smooth transition
        }, 50); // Reduced from 100ms to 50ms
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center max-w-md"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mb-8"
            >
              <Loader className="w-24 h-24 text-white/60 mx-auto" />
            </motion.div>
            <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-4">
              Loading Order Details
            </h2>
            <p className="text-white/70 mb-8 text-lg">
              Please wait while we fetch your order information...
            </p>
          </motion.div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !order) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center max-w-md"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="mb-8"
            >
              <AlertCircle className="w-24 h-24 text-red-400/60 mx-auto" />
            </motion.div>
            <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-red-100 to-orange-100 bg-clip-text text-transparent mb-4">
              {error ? 'Error Loading Order' : 'Order Not Found'}
            </h2>
            <p className="text-white/70 mb-8 text-lg">
              {error || "The order you're looking for doesn't exist or has been removed."}
            </p>
            <div className="flex gap-4 justify-center">
              <motion.button
                onClick={() => window.location.reload()}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-8 rounded-2xl font-semibold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300"
              >
                Try Again
              </motion.button>
              <motion.button
                onClick={() => navigate('/customer/orders')}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-gray-600 to-gray-700 text-white py-4 px-8 rounded-2xl font-semibold shadow-2xl hover:shadow-gray-500/25 transition-all duration-300"
              >
                Back to Orders
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Confirmed': return 'bg-blue-100 text-blue-800';
      case 'Preparing': return 'bg-orange-100 text-orange-800';
      case 'Out for Delivery': return 'bg-purple-100 text-purple-800';
      case 'Delivered': return 'bg-green-100 text-green-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleTrackOrder = () => {
    if (order?.id) {
      navigate(`/customer/order-tracking?orderId=${order.id}`);
    }
  };

  const handleReorder = () => {
    if (order?.supplier?.id) {
      // Navigate to supplier details page to reorder
      navigate(`/customer/supplier-details?supplierId=${order.supplier.id}`);
    }
  };

  const handleContactSupplier = () => {
    // Use supplier phone if available, fallback to order phone
    const phoneNumber = order?.phone;
    if (phoneNumber) {
      window.open(`tel:${phoneNumber}`);
    } else {
      alert('Supplier contact information not available');
    }
  };

  const handleContactDriver = () => {
    if (order?.driverPhone) {
      window.open(`tel:${order.driverPhone}`);
    } else {
      alert('Driver contact information not available');
    }
  };

  const handleRateOrder = () => {
    // TODO: Implement rating functionality
    alert('Rating functionality will be implemented soon');
  };

  const handleSaveFavorite = () => {
    // TODO: Implement save to favorites functionality
    alert('Save to favorites functionality will be implemented soon');
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-indigo-500/20 to-cyan-500/20 rounded-full blur-3xl"
        />
      </div>

      {/* Sticky Header with Ultra-Smooth Scroll Animation */}
      <motion.div
        className="fixed left-0 right-0"
        animate={{
          top: isHeaderCompact ? "0px" : "64px",
          zIndex: isHeaderCompact ? 50 : 40,
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{
          duration: 0.6,
          ease: [0.25, 0.46, 0.45, 0.94], // Custom cubic-bezier for ultra-smooth feel
          type: "tween"
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "1.5rem",
              paddingBottom: isHeaderCompact ? "1rem" : "1.5rem",
            }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
          >
            <div className="flex items-center gap-4">
              <motion.button
                onClick={() => navigate(-1)}
                whileHover={{
                  scale: 1.1,
                  x: -2,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { duration: 0.15, ease: "easeInOut" }
                }}
                className="p-3 hover:bg-white/10 rounded-xl backdrop-blur-sm border border-white/10"
                style={{
                  transition: "background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                }}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>
              <div>
                <motion.h1
                  className="font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                  animate={{
                    fontSize: isHeaderCompact ? "1.5rem" : "1.75rem",
                  }}
                  transition={{
                    duration: 0.6,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    type: "tween"
                  }}
                >
                  Order Details
                </motion.h1>
                <motion.p
                  className="text-white/60"
                  animate={{
                    fontSize: isHeaderCompact ? "0.875rem" : "1rem",
                    opacity: isHeaderCompact ? 0.8 : 1,
                  }}
                  transition={{
                    duration: 0.6,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    type: "tween"
                  }}
                >
                  Order #{order.id}
                </motion.p>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Content - Ultra-smooth dynamic padding based on header state */}
      <motion.div
        className="relative z-10 pb-20"
        animate={{
          paddingTop: isHeaderCompact ? "80px" : "160px",
        }}
        transition={{
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "tween"
        }}
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
          {/* Order Status - Ultra-smooth Enhanced */}
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.1,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl"
                >
                  <Package className="w-6 h-6 text-white" />
                </motion.div>
                Order Status
              </h2>
              <motion.span
                whileHover={{ scale: 1.05 }}
                className={`px-6 py-3 rounded-2xl text-sm font-bold shadow-lg ${getStatusColor(order.status)} border border-white/20`}
              >
                {order.status}
              </motion.span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <motion.div
                whileHover={{
                  scale: 1.03,
                  y: -4,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl border border-white/10"
              >
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl">
                  <Clock className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-white">Placed At</p>
                  <p className="text-white/70">{formatDate(order.placedAt)}</p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{
                  scale: 1.03,
                  y: -4,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl border border-white/10"
              >
                <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl">
                  <Clock className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-white">Estimated Time</p>
                  <p className="text-white/70">{order.estimatedTime}</p>
                </div>
              </motion.div>
            </div>

            {order.status !== 'Delivered' && order.status !== 'Cancelled' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mt-6"
              >
                <motion.button
                  onClick={handleTrackOrder}
                  whileHover={{
                    scale: 1.06,
                    y: -4,
                    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                  }}
                  whileTap={{
                    scale: 0.96,
                    transition: { duration: 0.15, ease: "easeInOut" }
                  }}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-2xl font-semibold shadow-2xl hover:shadow-purple-500/25 flex items-center gap-3"
                  style={{
                    transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                  }}
                >
                  <Navigation className="w-5 h-5" />
                  Track Order Live
                </motion.button>
              </motion.div>
            )}
          </motion.div>

          {/* Supplier Information - Ultra-smooth Enhanced */}
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.2,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
          >
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl"
              >
                <User className="w-6 h-6 text-white" />
              </motion.div>
              Supplier Information
            </h2>

            <motion.div
              whileHover={{
                scale: 1.03,
                y: -6,
                transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              className="flex items-center gap-6 p-6 bg-white/5 rounded-2xl border border-white/10"
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="w-20 h-20 rounded-2xl bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center shadow-2xl"
              >
                <Package className="w-10 h-10 text-white" />
              </motion.div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-white mb-2">{order.supplier?.name || 'Unknown Supplier'}</h3>
                <div className="flex items-center gap-2">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, delay: i * 0.1, repeat: Infinity }}
                    >
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    </motion.div>
                  ))}
                  <span className="text-white/70 ml-2 font-semibold">4.8 (Premium)</span>
                </div>
              </div>
              <motion.button
                onClick={handleContactSupplier}
                whileHover={{
                  scale: 1.06,
                  y: -4,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                whileTap={{
                  scale: 0.96,
                  transition: { duration: 0.15, ease: "easeInOut" }
                }}
                className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl text-white font-semibold shadow-2xl hover:shadow-green-500/25"
                style={{
                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                }}
              >
                <Phone className="w-5 h-5" />
                Contact Supplier
              </motion.button>
            </motion.div>
          </motion.div>

          {/* Order Items - Ultra-smooth Enhanced */}
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.3,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl"
                >
                  <Package className="w-6 h-6 text-white" />
                </motion.div>
                Order Items
              </h2>
              <motion.button
                onClick={() => setExpandedSections(prev => ({ ...prev, items: !prev.items }))}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 bg-white/10 rounded-xl border border-white/20"
              >
                {expandedSections.items ?
                  <ChevronUp className="w-5 h-5 text-white" /> :
                  <ChevronDown className="w-5 h-5 text-white" />
                }
              </motion.button>
            </div>

            <AnimatePresence>
              {expandedSections.items && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{
                    duration: 0.5,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    type: "tween"
                  }}
                  className="space-y-4"
                >
                  {(order.items || []).map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -30, scale: 0.95 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      transition={{
                        delay: index * 0.15,
                        duration: 0.6,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "tween"
                      }}
                      whileHover={{
                        scale: 1.03,
                        y: -4,
                        transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                      }}
                      className="flex items-center gap-6 p-6 bg-white/5 rounded-2xl border border-white/10"
                    >
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        className="relative"
                      >
                        {item.product?.image ? (
                          <img
                            src={item.product.image}
                            alt={item.product?.name || 'Product'}
                            className="w-20 h-20 rounded-2xl object-cover shadow-2xl"
                          />
                        ) : (
                          <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-gray-500 to-gray-600 flex items-center justify-center shadow-2xl">
                            <Package className="w-8 h-8 text-white/60" />
                          </div>
                        )}
                        <div className="absolute -top-2 -right-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                          {item.qty || 1}
                        </div>
                      </motion.div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold text-white mb-1">{item.product?.name || 'Unknown Item'}</h4>
                        <p className="text-white/70 mb-2">Quantity: {item.qty || 1} items</p>

                        {/* Product Options */}
                        <div className="space-y-1">
                          {/* Selected Size */}
                          {item.selectedSize && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs bg-blue-500/20 text-blue-200 px-2 py-1 rounded-full">
                                Size: {item.selectedSize}
                              </span>
                            </div>
                          )}

                          {/* Selected Color */}
                          {item.selectedColor && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs bg-purple-500/20 text-purple-200 px-2 py-1 rounded-full">
                                Color: {item.selectedColor}
                              </span>
                            </div>
                          )}

                          {/* Selected Additions */}
                          {item.selectedAdditions && item.selectedAdditions.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {item.selectedAdditions.map((addition, addIndex) => (
                                <span key={addIndex} className="text-xs bg-green-500/20 text-green-200 px-2 py-1 rounded-full">
                                  + {addition.name} (+₪{addition.price})
                                </span>
                              ))}
                            </div>
                          )}

                          {/* Selected Sides */}
                          {item.selectedSides && item.selectedSides.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {item.selectedSides.map((side, sideIndex) => (
                                <span key={sideIndex} className="text-xs bg-orange-500/20 text-orange-200 px-2 py-1 rounded-full">
                                  Side: {side.name} (+₪{side.price})
                                </span>
                              ))}
                            </div>
                          )}

                          {/* Without Items */}
                          {item.without && item.without.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {item.without.map((withoutItem, withoutIndex) => (
                                <span key={withoutIndex} className="text-xs bg-red-500/20 text-red-200 px-2 py-1 rounded-full">
                                  Without: {withoutItem}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-white">₪{(item.finalPrice || 0).toFixed(2)}</p>
                        <p className="text-white/70">₪{(item.product.price || 0).toFixed(2)} each</p>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Delivery Information - Enhanced */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                <motion.div
                  animate={{ y: [0, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl"
                >
                  <MapPin className="w-6 h-6 text-white" />
                </motion.div>
                Delivery Information
              </h2>
              <motion.button
                onClick={() => setExpandedSections(prev => ({ ...prev, delivery: !prev.delivery }))}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 bg-white/10 rounded-xl border border-white/20"
              >
                {expandedSections.delivery ?
                  <ChevronUp className="w-5 h-5 text-white" /> :
                  <ChevronDown className="w-5 h-5 text-white" />
                }
              </motion.button>
            </div>

            <AnimatePresence>
              {expandedSections.delivery && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <motion.div
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="flex items-start gap-4 p-6 bg-white/5 rounded-2xl border border-white/10"
                  >
                    <div className="p-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl">
                      <MapPin className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-white text-lg mb-2">Delivery Address</p>
                      <p className="text-white/70 text-lg">{order.address || 'Address not provided'}</p>
                    </div>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="flex items-center gap-4 p-6 bg-white/5 rounded-2xl border border-white/10"
                  >
                    <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl">
                      <Phone className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-white text-lg mb-2">Contact Number</p>
                      <p className="text-white/70 text-lg">{order.phone || 'Phone not provided'}</p>
                    </div>
                  </motion.div>

                  {order.notes && (
                    <motion.div
                      whileHover={{ scale: 1.02, y: -2 }}
                      className="flex items-start gap-4 p-6 bg-white/5 rounded-2xl border border-white/10"
                    >
                      <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl">
                        <MessageSquare className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <p className="font-bold text-white text-lg mb-2">Special Instructions</p>
                        <p className="text-white/70 text-lg">{order.notes}</p>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Driver Information - Enhanced */}
          {order.driverName && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
            >
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                  className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl"
                >
                  <User className="w-6 h-6 text-white" />
                </motion.div>
                Driver Information
              </h2>

              <motion.div
                whileHover={{ scale: 1.02, y: -2 }}
                className="flex items-center gap-6 p-6 bg-white/5 rounded-2xl border border-white/10"
              >
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 360 }}
                  transition={{ duration: 0.5 }}
                  className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-2xl"
                >
                  <User className="w-8 h-8 text-white" />
                </motion.div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-2">{order.driverName}</h3>
                  <div className="flex items-center gap-2">
                    {[...Array(5)].map((_, i) => (
                      <motion.div
                        key={i}
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, delay: i * 0.1, repeat: Infinity }}
                      >
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      </motion.div>
                    ))}
                    <span className="text-white/70 ml-2 font-semibold">4.9 (Excellent)</span>
                  </div>
                </div>
                {order.driverPhone && (
                  <motion.button
                    onClick={handleContactDriver}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl text-white font-semibold shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300"
                  >
                    <Phone className="w-5 h-5" />
                    Contact Driver
                  </motion.button>
                )}
              </motion.div>
            </motion.div>
          )}

          {/* Payment Summary - Enhanced */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl"
                >
                  <CreditCard className="w-6 h-6 text-white" />
                </motion.div>
                Payment Summary
              </h2>
              <motion.button
                onClick={() => setExpandedSections(prev => ({ ...prev, payment: !prev.payment }))}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 bg-white/10 rounded-xl border border-white/20"
              >
                {expandedSections.payment ?
                  <ChevronUp className="w-5 h-5 text-white" /> :
                  <ChevronDown className="w-5 h-5 text-white" />
                }
              </motion.button>
            </div>

            <AnimatePresence>
              {expandedSections.payment && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="space-y-4">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="flex justify-between items-center p-4 bg-white/5 rounded-2xl border border-white/10"
                    >
                      <span className="text-white text-lg">Subtotal</span>
                      <span className="text-white font-bold text-lg">₪{(order.subtotal || 0).toFixed(2)}</span>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="flex justify-between items-center p-4 bg-white/5 rounded-2xl border border-white/10"
                    >
                      <span className="text-white text-lg">Delivery Fee</span>
                      <span className="text-white font-bold text-lg">₪{(order.deliveryFee || 0).toFixed(2)}</span>
                    </motion.div>
                    {(order.promoDiscount && order.promoDiscount > 0) ? (
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="flex justify-between items-center p-4 bg-white/5 rounded-2xl border border-white/10"
                      >
                        <span className="text-white text-lg">Promo Discount</span>
                        <span className="text-green-400 font-bold text-lg">-₪{(order.promoDiscount || 0).toFixed(2)}</span>
                      </motion.div>
                    ) : null}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="flex justify-between items-center p-6 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-2xl border border-emerald-400/30"
                    >
                      <span className="text-white text-xl font-bold">Total</span>
                      <span className="text-white font-bold text-2xl">₪{(order.total || 0).toFixed(2)}</span>
                    </motion.div>
                  </div>

                  <motion.div
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="flex items-center gap-4 p-6 bg-white/5 rounded-2xl border border-white/10"
                  >
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl">
                      <CreditCard className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="font-bold text-white text-lg mb-1">Payment Method</p>
                      <p className="text-white/70 text-lg capitalize">{order.paymentMethod || 'cash'}</p>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Action Buttons - Ultra-smooth Enhanced */}
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.7,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            {/* Track Order Button - Show for non-delivered orders */}
            {order.status !== 'Delivered' && order.status !== 'Cancelled' && (
              <motion.button
                onClick={handleTrackOrder}
                whileHover={{
                  scale: 1.06,
                  y: -6,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                whileTap={{
                  scale: 0.96,
                  transition: { duration: 0.15, ease: "easeInOut" }
                }}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-2xl font-bold shadow-2xl hover:shadow-blue-500/25 flex items-center justify-center gap-3"
                style={{
                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Navigation className="w-6 h-6" />
                </motion.div>
                Track Live
              </motion.button>
            )}

            <motion.button
              onClick={handleReorder}
              whileHover={{
                scale: 1.06,
                y: -6,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              whileTap={{
                scale: 0.96,
                transition: { duration: 0.15, ease: "easeInOut" }
              }}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-8 rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/25 flex items-center justify-center gap-3"
              style={{
                transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
              }}
            >
              <RotateCcw className="w-6 h-6" />
              Reorder
            </motion.button>

            {order.status === 'Delivered' && (
              <motion.button
                onClick={handleRateOrder}
                whileHover={{
                  scale: 1.06,
                  y: -6,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                whileTap={{
                  scale: 0.96,
                  transition: { duration: 0.15, ease: "easeInOut" }
                }}
                className="bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 px-8 rounded-2xl font-bold shadow-2xl hover:shadow-yellow-500/25 flex items-center justify-center gap-3"
                style={{
                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                }}
              >
                <Star className="w-6 h-6" />
                Rate Order
              </motion.button>
            )}

            <motion.button
              onClick={handleSaveFavorite}
              whileHover={{
                scale: 1.06,
                y: -6,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              whileTap={{
                scale: 0.96,
                transition: { duration: 0.15, ease: "easeInOut" }
              }}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-4 px-8 rounded-2xl font-bold shadow-2xl hover:shadow-emerald-500/25 flex items-center justify-center gap-3"
              style={{
                transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
              }}
            >
              <Heart className="w-6 h-6" />
              Save Favorite
            </motion.button>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default OrderDetailsPage;