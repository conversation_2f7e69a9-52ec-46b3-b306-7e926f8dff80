import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { ArrowLeft, Plus, Minus, ShoppingCart, Check, Star, Heart, Share2, Clock, Award, Sparkles, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCartStore } from '../../stores/cartStore';
import { apiService } from '../../services/api';
import type { Product, Supplier } from '../../services/api';

type Addition = {
  id: string;
  name: string;
  price: number;
};

const SupplierProductDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const productId = searchParams.get('productId');
  const supplierId = searchParams.get('supplierId');
  const category = searchParams.get('category');

  // State for dynamic data
  const [product, setProduct] = useState<Product | null>(null);
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);

  const { addItem } = useCartStore();

  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [selectedAdditions, setSelectedAdditions] = useState<string[]>([]);
  const [selectedSides, setSelectedSides] = useState<string[]>([]);
  const [without, setWithout] = useState<string[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageGallery, setShowImageGallery] = useState(false);

  // Fetch data from backend
  useEffect(() => {
    const fetchData = async () => {
      if (!supplierId || !productId) {
        setError('Missing supplier or product ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Try to get product from location state first (for navigation from supplier page)
        const productFromState = location.state?.product;
        const supplierFromState = location.state?.supplier;

        if (productFromState && supplierFromState) {
          // Always fetch fresh supplier data to get the latest ratings field
          try {
            const freshSupplierResponse = await apiService.getSupplierById(supplierId);
            if (freshSupplierResponse.success && freshSupplierResponse.data) {
              setSupplier(freshSupplierResponse.data);
            } else {
              // Fallback to cached data if API fails
              setSupplier(supplierFromState);
            }
          } catch (error) {
            console.error('Failed to fetch fresh supplier data:', error);
            // Fallback to cached data if API fails
            setSupplier(supplierFromState);
          }

          setProduct(productFromState);
          setSelectedSize(productFromState.clothingOptions?.sizes?.[0] || '');
          setSelectedColor(productFromState.clothingOptions?.colors?.[0] || '');
          setLoading(false);
          return;
        }

        // Fetch supplier data and products
        const [supplierResponse, productsResponse] = await Promise.all([
          apiService.getSupplierById(supplierId),
          apiService.getSupplierProducts(supplierId)
        ]);

        if (!supplierResponse.success || !productsResponse.success || !supplierResponse.data || !productsResponse.data) {
          throw new Error('Failed to fetch data');
        }

        const supplierData = supplierResponse.data;
        const productsData = productsResponse.data;

        setSupplier(supplierData);
        setAllProducts(productsData.products);

        // Find the specific product
        const foundProduct = productsData.products.find((p: Product) => p.id === productId);
        if (!foundProduct) {
          throw new Error('Product not found');
        }

        setProduct(foundProduct);
        setSelectedSize(foundProduct.clothingOptions?.sizes?.[0] || '');
        setSelectedColor(foundProduct.clothingOptions?.colors?.[0] || '');

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load product data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supplierId, productId, location.state]);

  // Scroll effect for hero image
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const increaseQuantity = () => setQuantity(prev => prev + 1);
  const decreaseQuantity = () => setQuantity(prev => Math.max(1, prev - 1));

  const toggleAddition = (additionId: string) => {
    setSelectedAdditions(prev =>
      prev.includes(additionId)
        ? prev.filter(id => id !== additionId)
        : [...prev, additionId]
    );
  };

  const toggleSide = (sideId: string) => {
    setSelectedSides(prev =>
      prev.includes(sideId)
        ? prev.filter(id => id !== sideId)
        : [...prev, sideId]
    );
  };

  const toggleWithout = (item: string) => {
    setWithout(prev =>
      prev.includes(item)
        ? prev.filter(w => w !== item)
        : [...prev, item]
    );
  };

  const calculateTotalPrice = () => {
    if (!product) return 0;

    // Use discountPrice if available, otherwise use regular price
    const basePrice = (product.discountPrice && product.discountPrice > 0) ? product.discountPrice : product.price;

    const additionsPrice = selectedAdditions.reduce((sum, additionId) => {
      const addition = product.restaurantOptions?.additions?.find((a: { id: string; name: string; price: number }) => a.id === additionId);
      return sum + (addition?.price || 0);
    }, 0);
    const sidesPrice = selectedSides.reduce((sum, sideId) => {
      const side = product.restaurantOptions?.sides?.find((s: { id: string; name: string; price: number }) => s.id === sideId);
      return sum + (side?.price || 0);
    }, 0);

    return (basePrice + additionsPrice + sidesPrice) * quantity;
  };

  const handleAddToCart = () => {
    if (!product || !supplier) return;

    // Check if product is available
    if (!product.isAvailable) {
      setError('This product is currently unavailable');
      return;
    }

    const finalPrice = calculateTotalPrice() / quantity; // price per item including options

    if (category === 'restaurants') {
      addItem({
        product,
        qty: quantity,
        finalPrice,
        supplierId: supplier.id,
        supplierName: supplier.name,
        supplierCategory: 'restaurants',
        selectedAdditions: product.restaurantOptions?.additions?.filter((a: Addition) => selectedAdditions.includes(a.id)) || [],
        selectedSides: product.restaurantOptions?.sides?.filter((s: Addition) => selectedSides.includes(s.id)) || [],
        without,
      });
    } else {
      addItem({
        product,
        qty: quantity,
        finalPrice,
        supplierId: supplier.id,
        supplierName: supplier.name,
        supplierCategory: 'clothings',
        selectedSize,
        selectedColor,
      });
    }

    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 2000);
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 mx-auto mb-4"
            >
              <Loader2 className="w-16 h-16 text-white" />
            </motion.div>
            <h2 className="text-2xl font-bold text-white mb-2">Loading Product Details</h2>
            <p className="text-white/70">Please wait while we fetch the product information...</p>
          </motion.div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-red-500/30 to-pink-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center max-w-md mx-auto px-4"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full flex items-center justify-center">
              <span className="text-red-400 text-2xl">⚠️</span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Error Loading Product</h2>
            <p className="text-white/70 mb-6">{error}</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(-1)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-xl font-semibold"
            >
              Go Back
            </motion.button>
          </motion.div>
        </div>
      </div>
    );
  }

  // Product not found state
  if (!product || !supplier) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-yellow-500/30 to-orange-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center max-w-md mx-auto px-4"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-yellow-500/20 rounded-full flex items-center justify-center">
              <span className="text-yellow-400 text-2xl">🔍</span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Product Not Found</h2>
            <p className="text-white/70 mb-6">The product you're looking for could not be found.</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(-1)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-xl font-semibold"
            >
              Go Back
            </motion.button>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
                {/* Animated gradient orbs */}
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.6, 0.3],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
                />
                <motion.div
                  animate={{
                    scale: [1.2, 1, 1.2],
                    opacity: [0.4, 0.7, 0.4],
                  }}
                  transition={{
                    duration: 10,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 2
                  }}
                  className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
                />
                <motion.div
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.2, 0.5, 0.2],
                  }}
                  transition={{
                    duration: 12,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 4
                  }}
                  className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
                />
      
                {/* Floating particles */}
                {[...Array(15)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-white/20 rounded-full"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                    }}
                    animate={{
                      y: [-20, -100, -20],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 3 + Math.random() * 2,
                      repeat: Infinity,
                      delay: Math.random() * 2,
                    }}
                  />
                ))}
      </div>

      {/* Premium Success Notification */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5, y: 100, rotate: -10 }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0,
              rotate: 0,
              transition: {
                type: "spring",
                stiffness: 300,
                damping: 20,
                duration: 0.6
              }
            }}
            exit={{
              opacity: 0,
              scale: 0.5,
              y: -50,
              rotate: 10,
              transition: { duration: 0.3 }
            }}
            className="fixed bottom-32 inset-x-0 z-50 flex justify-center"
          >
            <motion.div
              animate={{
                boxShadow: [
                  "0 20px 40px rgba(34, 197, 94, 0.3)",
                  "0 25px 50px rgba(34, 197, 94, 0.5)",
                  "0 20px 40px rgba(34, 197, 94, 0.3)"
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
              className="bg-gradient-to-r from-green-500 via-emerald-500 to-green-600 text-white px-8 py-5 rounded-3xl border-2 border-white/30 backdrop-blur-xl relative overflow-hidden max-w-md"
            >
              {/* Animated background pattern */}
              <motion.div
                animate={{
                  x: ["-100%", "100%"],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "linear"
                }}
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              />

              {/* Success content */}
              <div className="relative z-10 flex items-center gap-4">
                <motion.div
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    rotate: { duration: 0.6, ease: "easeOut" },
                    scale: { duration: 1, repeat: Infinity, repeatType: "reverse" }
                  }}
                  className="p-3 bg-white/25 rounded-2xl backdrop-blur-sm border border-white/30"
                >
                  <Check className="w-6 h-6 text-white" />
                </motion.div>

                <div className="flex flex-col">
                  <motion.span
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="font-bold text-xl"
                  >
                    Successfully Added!
                  </motion.span>
                  <motion.p
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-green-100 text-sm font-medium"
                  >
                    {quantity} × {product.name} • ₪{calculateTotalPrice().toFixed(2)}
                  </motion.p>
                </div>

                {/* Sparkle effects */}
                <div className="absolute -top-2 -right-2">
                  <motion.div
                    animate={{
                      scale: [0, 1, 0],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: 0.5
                    }}
                  >
                    <Sparkles className="w-4 h-4 text-yellow-300" />
                  </motion.div>
                </div>
                <div className="absolute -bottom-1 -left-1">
                  <motion.div
                    animate={{
                      scale: [0, 1, 0],
                      rotate: [360, 180, 0],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: 1
                    }}
                  >
                    <Sparkles className="w-3 h-3 text-blue-300" />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Premium Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed top-16 left-0 right-0 z-40 bg-white/10 backdrop-blur-xl border-b border-white/20"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => navigate(-1)}
                className="p-3 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-all duration-300 border border-white/30"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>
              <div>
                <h1 className="text-xl font-bold text-white">{product.name}</h1>
                <p className="text-white/70 text-sm">{supplier.name}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setIsLiked(!isLiked)}
                className="p-3 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-all duration-300 border border-white/30"
              >
                <Heart className={`w-5 h-5 ${isLiked ? 'text-red-400 fill-red-400' : 'text-white'}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-3 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-all duration-300 border border-white/30"
              >
                <Share2 className="w-5 h-5 text-white" />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <div className="relative z-10 pt-20 pb-40">
        {/* Premium Hero Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="relative h-[500px] overflow-hidden mx-4 rounded-3xl mt-4 mb-8"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-transparent z-10"></div>
          <motion.img
            src={product.image}
            alt={product.name}
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.2, ease: "easeOut" }}
            className="w-full h-full object-cover"
            style={{
              transform: `scale(${1 + scrollY * 0.0003})`,
            }}
            whileHover={{ scale: 1.05 }}
          />

          {/* Floating Info Cards */}
          <div className="absolute inset-0 z-20 p-6 flex flex-col justify-between">
            {/* Top badges */}
            <div className="flex justify-between items-start">
              <div className="flex gap-2">
                <motion.div
                  initial={{ opacity: 0, x: -50, scale: 0.8 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  transition={{ delay: 0.6, duration: 0.8, type: "spring", stiffness: 100 }}
                  className="bg-gradient-to-r from-yellow-400 to-orange-500 px-4 py-2 rounded-full flex items-center gap-2 shadow-lg"
                >
                  <Award className="w-4 h-4 text-white" />
                  <span className="text-white font-bold text-sm">Premium</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -50, scale: 0.8 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  transition={{ delay: 0.8, duration: 0.8, type: "spring", stiffness: 100 }}
                  className="bg-white/20 backdrop-blur-xl px-4 py-2 rounded-full flex items-center gap-2 border border-white/30 shadow-lg"
                >
                  <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                  <span className="text-white font-semibold text-sm">{supplier.rating || 0.0}</span>
                  <span className="text-white/70 text-sm">({supplier.ratings})</span>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, x: 50, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                transition={{ delay: 1.0, duration: 0.8, type: "spring", stiffness: 100 }}
                className="bg-white/20 backdrop-blur-xl px-4 py-2 rounded-full flex items-center gap-2 border border-white/30 shadow-lg"
              >
                <Clock className="w-4 h-4 text-green-400" />
                <span className="text-white font-semibold text-sm">{supplier.deliveryTime}</span>
              </motion.div>
            </div>

            {/* Bottom info */}
            <div>
              <motion.h2
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 1.2, duration: 0.8, type: "spring", stiffness: 100 }}
                className="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-lg"
              >
                {product.name}
              </motion.h2>
              <motion.div
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 1.4, duration: 0.8, type: "spring", stiffness: 100 }}
                className="flex items-center gap-4 flex-wrap"
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-4 rounded-2xl shadow-2xl border border-white/20"
                >
                  {product.discountPrice && product.discountPrice > 0 ? (
                    <div className="flex items-center gap-2">
                      <span className="text-white/70 font-semibold text-xl line-through">₪{product.price.toFixed(2)}</span>
                      <span className="text-white font-bold text-3xl">₪{product.discountPrice.toFixed(2)}</span>
                    </div>
                  ) : (
                    <span className="text-white font-bold text-3xl">₪{product.price.toFixed(2)}</span>
                  )}
                </motion.div>
                {product.description && (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="bg-white/20 backdrop-blur-xl px-6 py-3 rounded-xl border border-white/30 shadow-lg max-w-md"
                  >
                    <span className="text-white/90 text-base font-medium">{product.description}</span>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Premium Content Sections */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8 mt-8">

          {/* Restaurant Options */}
          {category === 'restaurants' && (
            <>
              {/* Additions */}
              {product.restaurantOptions?.additions && product.restaurantOptions.additions.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">Premium Additions</h3>
                  </div>
                  <div className="space-y-4">
                    {product.restaurantOptions.additions.map((addition: Addition, index: number) => (
                      <motion.label
                        key={addition.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.9 + index * 0.1 }}
                        whileHover={{ scale: 1.02, x: 10 }}
                        className="flex items-center justify-between p-4 bg-white/10 backdrop-blur-sm rounded-2xl cursor-pointer hover:bg-white/20 transition-all duration-300 border border-white/20 group"
                      >
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <input
                              type="checkbox"
                              checked={selectedAdditions.includes(addition.id)}
                              onChange={() => toggleAddition(addition.id)}
                              className="sr-only"
                            />
                            <motion.div
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              className={`w-6 h-6 rounded-xl border-2 transition-all duration-300 cursor-pointer flex items-center justify-center ${
                                selectedAdditions.includes(addition.id)
                                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 border-purple-400 shadow-lg shadow-purple-500/25'
                                  : 'bg-white/10 border-white/30 hover:border-white/50 hover:bg-white/20'
                              }`}
                            >
                              {selectedAdditions.includes(addition.id) && (
                                <motion.div
                                  initial={{ scale: 0, rotate: -180 }}
                                  animate={{ scale: 1, rotate: 0 }}
                                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                                >
                                  <Check className="w-4 h-4 text-white" />
                                </motion.div>
                              )}
                            </motion.div>
                          </div>
                          <span className="font-semibold text-white text-lg group-hover:text-purple-200 transition-colors">{addition.name}</span>
                        </div>
                        {addition.price > 0 && (
                          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 px-4 py-2 rounded-xl">
                            <span className="text-white font-bold">+₪{addition.price}</span>
                          </div>
                        )}
                      </motion.label>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Without Options */}
              {product.restaurantOptions?.without && product.restaurantOptions.without.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.0 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl">
                      <Minus className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">Customize Your Order</h3>
                  </div>
                  <div className="space-y-4">
                    {product.restaurantOptions.without.map((item: string, index: number) => (
                      <motion.label
                        key={item}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 1.1 + index * 0.1 }}
                        whileHover={{ scale: 1.02, x: 10 }}
                        className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-2xl cursor-pointer hover:bg-white/20 transition-all duration-300 border border-white/20 group"
                      >
                        <div className="relative">
                          <input
                            type="checkbox"
                            checked={without.includes(item)}
                            onChange={() => toggleWithout(item)}
                            className="sr-only"
                          />
                          <motion.div
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            className={`w-6 h-6 rounded-xl border-2 transition-all duration-300 cursor-pointer flex items-center justify-center ${
                              without.includes(item)
                                ? 'bg-gradient-to-r from-red-500 to-pink-500 border-red-400 shadow-lg shadow-red-500/25'
                                : 'bg-white/10 border-white/30 hover:border-white/50 hover:bg-white/20'
                            }`}
                          >
                            {without.includes(item) && (
                              <motion.div
                                initial={{ scale: 0, rotate: -180 }}
                                animate={{ scale: 1, rotate: 0 }}
                                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                              >
                                <Check className="w-4 h-4 text-white" />
                              </motion.div>
                            )}
                          </motion.div>
                        </div>
                        <span className="font-semibold text-white text-lg group-hover:text-red-200 transition-colors">Without {item}</span>
                      </motion.label>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Sides */}
              {product.restaurantOptions?.sides && product.restaurantOptions.sides.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl">
                      <Plus className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">Perfect Side Orders</h3>
                  </div>
                  <div className="space-y-4">
                    {product.restaurantOptions.sides.map((side: Addition, index: number) => (
                      <motion.label
                        key={side.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 1.3 + index * 0.1 }}
                        whileHover={{ scale: 1.02, x: 10 }}
                        className="flex items-center justify-between p-4 bg-white/10 backdrop-blur-sm rounded-2xl cursor-pointer hover:bg-white/20 transition-all duration-300 border border-white/20 group"
                      >
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <input
                              type="checkbox"
                              checked={selectedSides.includes(side.id)}
                              onChange={() => toggleSide(side.id)}
                              className="sr-only"
                            />
                            <motion.div
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              className={`w-6 h-6 rounded-xl border-2 transition-all duration-300 cursor-pointer flex items-center justify-center ${
                                selectedSides.includes(side.id)
                                  ? 'bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 shadow-lg shadow-green-500/25'
                                  : 'bg-white/10 border-white/30 hover:border-white/50 hover:bg-white/20'
                              }`}
                            >
                              {selectedSides.includes(side.id) && (
                                <motion.div
                                  initial={{ scale: 0, rotate: -180 }}
                                  animate={{ scale: 1, rotate: 0 }}
                                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                                >
                                  <Check className="w-4 h-4 text-white" />
                                </motion.div>
                              )}
                            </motion.div>
                          </div>
                          <span className="font-semibold text-white text-lg group-hover:text-green-200 transition-colors">{side.name}</span>
                        </div>
                        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 px-4 py-2 rounded-xl">
                          <span className="text-white font-bold">+₪{side.price}</span>
                        </div>
                      </motion.label>
                    ))}
                  </div>
                </motion.div>
              )}
            </>
          )}

          {/* Clothing Options */}
          {category === 'clothings' && (
            <>
              {/* Image Gallery */}
              {product.clothingOptions?.gallery && product.clothingOptions.gallery.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">Product Gallery</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    {product.clothingOptions.gallery.map((image: string, index: number) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.9 + index * 0.1 }}
                        whileHover={{ scale: 1.05 }}
                        className="relative group cursor-pointer"
                        onClick={() => {
                          setCurrentImageIndex(index);
                          setShowImageGallery(true);
                        }}
                      >
                        <img
                          src={image}
                          alt={`${product.name} ${index + 1}`}
                          className="w-full h-40 object-cover rounded-2xl border border-white/20"
                        />
                        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl flex items-center justify-center">
                          <div className="bg-white/20 backdrop-blur-sm p-3 rounded-full">
                            <Sparkles className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Size Selection */}
              {product.clothingOptions?.sizes && product.clothingOptions.sizes.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.0 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl">
                      <Award className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">Select Your Size</h3>
                  </div>
                  <div className="flex flex-wrap gap-4">
                    {product.clothingOptions.sizes.map((size: string, index: number) => (
                      <motion.button
                        key={size}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1.1 + index * 0.1 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setSelectedSize(size)}
                        className={`px-6 py-4 rounded-2xl font-bold text-lg transition-all duration-300 border-2 ${
                          selectedSize === size
                            ? 'border-purple-400 bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25'
                            : 'border-white/30 bg-white/10 text-white hover:border-white/50 hover:bg-white/20'
                        }`}
                      >
                        {size}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Color Selection */}
              {product.clothingOptions?.colors && product.clothingOptions.colors.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-gradient-to-r from-pink-500 to-rose-500 rounded-2xl">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white">Choose Your Color</h3>
                  </div>
                  <div className="flex flex-wrap gap-4">
                    {product.clothingOptions.colors.map((color: string, index: number) => (
                      <motion.button
                        key={color}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1.3 + index * 0.1 }}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setSelectedColor(color)}
                        className={`px-6 py-4 rounded-2xl font-bold text-lg transition-all duration-300 border-2 ${
                          selectedColor === color
                            ? 'border-pink-400 bg-gradient-to-r from-pink-500 to-rose-500 text-white shadow-lg shadow-pink-500/25'
                            : 'border-white/30 bg-white/10 text-white hover:border-white/50 hover:bg-white/20'
                        }`}
                      >
                        {color}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </>
          )}


        </div>
      </div>

      {/* Professional Compact Add to Cart Section */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.6 }}
        className="fixed bottom-0 left-0 right-0 z-30 bg-black/20 backdrop-blur-xl border-t border-white/20 p-4"
      >
        <div className="max-w-4xl mx-auto flex items-center gap-4">
          {/* Quantity Controls */}
          <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-2xl p-2 border border-white/20">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={decreaseQuantity}
              className="w-10 h-10 rounded-xl bg-gradient-to-r from-red-500 to-pink-500 text-white flex items-center justify-center hover:shadow-lg transition-all duration-300"
            >
              <Minus className="w-4 h-4" />
            </motion.button>

            <div className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl border border-white/30 min-w-[3rem] text-center">
              <span className="text-white font-bold text-lg">{quantity}</span>
            </div>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={increaseQuantity}
              className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 text-white flex items-center justify-center hover:shadow-lg transition-all duration-300"
            >
              <Plus className="w-4 h-4" />
            </motion.button>
          </div>

          {/* Add to Cart Button */}
          <motion.button
            whileHover={{ scale: product.isAvailable ? 1.02 : 1 }}
            whileTap={{ scale: product.isAvailable ? 0.98 : 1 }}
            onClick={handleAddToCart}
            disabled={!product.isAvailable}
            className={`flex-1 py-4 rounded-2xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 border border-white/20 relative overflow-hidden group ${
              product.isAvailable
                ? 'bg-gradient-to-r from-purple-600 via-pink-600 to-purple-700 text-white hover:shadow-2xl hover:shadow-purple-500/25 cursor-pointer'
                : 'bg-gray-500/50 text-gray-300 cursor-not-allowed'
            }`}
          >
            {/* Animated background */}
            {product.isAvailable && (
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            )}

            {/* Button content */}
            <div className="relative z-10 flex items-center gap-3">
              <ShoppingCart className="w-5 h-5" />
              <span>
                {product.isAvailable
                  ? `Add to Cart • ₪${calculateTotalPrice().toFixed(2)}`
                  : 'Currently Unavailable'
                }
              </span>
            </div>
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default SupplierProductDetailsPage;