import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiService } from '../services/api';

type Location = {
  lat: number;
  lng: number;
  address?: string;
};

export type OrderItem = {
  product: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
  };
  qty: number;
  finalPrice: number;
  selectedAdditions?: Array<{ id: string; name: string; price: number }>;
  selectedSides?: Array<{ id: string; name: string; price: number }>;
  without?: string[];
  selectedSize?: string;
  selectedColor?: string;
};

export type Order = {
  id: string;
  createdAt: string;
  items: OrderItem[];
  supplier: {
    id: string;
    name: string;
  };
  subtotal: number;
  deliveryFee: number;
  promoDiscount: number;
  total: number;
  status: 'Pending' | 'Confirmed' | 'Preparing' | 'Ready' | 'On the Way' | 'Delivered' | 'Cancelled';
  address: string;
  phone: string;
  notes?: string;
  paymentMethod: 'cash' | 'card';
  promoCode?: string;
  estimatedTime: string;
  placedAt: string;
  driverName?: string;
  driverPhone?: string;
  driverLocation?: Location;
  deliveryLocation?: Location;
};

type PaginationInfo = {
  currentPage: number;
  totalPages: number;
  totalOrders: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
};

type OrdersState = {
  orders: Order[];
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  pagination: PaginationInfo | null;

  // Local state management
  addOrder: (order: Order) => void;
  addMultipleOrders: (orders: Order[]) => void;
  updateOrderStatus: (orderId: string, status: Order['status']) => void;
  getOrderById: (orderId: string) => Order | undefined;
  getOrdersByStatus: (status: Order['status']) => Order[];
  clearOrders: () => void;

  // Backend integration
  fetchOrders: (forceRefresh?: boolean, params?: any) => Promise<void>;
  refreshOrders: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  mapBackendStatus: (backendStatus: string) => Order['status'];
};

// Mock orders removed - now using real backend data only


export const useOrdersStore = create<OrdersState>()(
  persist(
    (set, get) => ({
      orders: [], // Start with empty orders - will be populated from backend
      isLoading: false,
      error: null,
      lastFetch: null,
      pagination: null,

      // Local state management
      addOrder: (order) => {
        set((state) => ({
          orders: [order, ...state.orders] // Add new order at the beginning
        }));
      },

      addMultipleOrders: (orders) => {
        set((state) => ({
          orders: [...orders, ...state.orders] // Add new orders at the beginning
        }));
      },

      updateOrderStatus: (orderId, status) => {
        set((state) => ({
          orders: state.orders.map((order) =>
            order.id === orderId ? { ...order, status } : order
          )
        }));
      },

      getOrderById: (orderId) => {
        return get().orders.find((order) => order.id === orderId);
      },

      getOrdersByStatus: (status) => {
        return get().orders.filter((order) => order.status === status);
      },

      clearOrders: () => {
        set({ orders: [], error: null });
      },

      setLoading: (isLoading) => {
        set({ isLoading });
      },

      setError: (error) => {
        set({ error });
      },

      // Backend integration with server-side pagination
      fetchOrders: async (forceRefresh = false, params = {}) => {
        const state = get();
        const now = Date.now();
        const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes (shorter for pagination)

        // Skip cache for pagination or search/filter changes
        const shouldSkipCache = forceRefresh || params.page > 1 || params.search || params.status;

        if (!shouldSkipCache && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION) {
          return;
        }

        set({ isLoading: true, error: null });

        try {
          // Fetch with server-side pagination parameters
          const response = await apiService.getUserOrders(params);

          if (response.success && response.data) {
            // Convert backend orders to local format - handle both array and object responses
            const ordersArray = Array.isArray(response.data) ? response.data : response.data.orders || [];

            const backendOrders = ordersArray.map((order: any) => {
              // Handle different possible field names from backend
              const orderId = order.orderId || order._id || order.id;
              const supplierName = order.supplierName || order.supplier?.name || 'Unknown Supplier';
              const supplierId = order.supplierId || order.supplier?.id || order.supplier?._id || '';

              // Handle items array with flexible structure matching backend IOrderItem
              const items = (order.items || []).map((item: any) => {
                const product = item.product || {};
                const selectedOptions = item.selectedOptions || {};

                return {
                  product: {
                    id: item.productId || product.id || product._id || '',
                    name: item.productName || product.name || item.name || 'Unknown Item',
                    image: item.productImage || product.image || item.image || '',
                    price: item.price || product.price || 0,
                    category: product.category || item.category || '',
                  },
                  qty: item.quantity || item.qty || 1,
                  finalPrice: item.subtotal || item.finalPrice || (item.price * (item.quantity || item.qty || 1)),
                  selectedAdditions: selectedOptions.additions || item.selectedAdditions || [],
                  selectedSides: selectedOptions.sides || item.selectedSides || [],
                  without: selectedOptions.without || item.without || [],
                  selectedSize: selectedOptions.size || item.selectedSize || '',
                  selectedColor: selectedOptions.color || item.selectedColor || '',
                };
              });

              // Handle delivery address with flexible structure
              const deliveryAddress = order.deliveryAddress || {};
              const coordinates = deliveryAddress.coordinates || {};

              return {
                id: orderId,
                createdAt: order.createdAt || order.placedAt || new Date().toISOString(),
                items,
                supplier: {
                  id: supplierId,
                  name: supplierName,
                },
                subtotal: order.subtotal || order.subTotal || 0,
                deliveryFee: order.deliveryFee || 5,
                promoDiscount: order.promoDiscount || order.discount || 0,
                total: order.totalAmount || order.total || 0,
                status: get().mapBackendStatus(order.status),
                address: deliveryAddress.street || deliveryAddress.address || order.address || '',
                phone: order.customerPhone || order.phone || '',
                notes: order.notes || '',
                paymentMethod: (order.paymentMethod as 'cash' | 'card') || 'cash',
                promoCode: order.promoCode || '',
                estimatedTime: order.estimatedDeliveryTime || order.estimatedTime || '45-60 mins',
                placedAt: order.createdAt || order.placedAt || new Date().toISOString(),
                driverName: order.driverName || '',
                driverPhone: order.driverPhone || '',
                driverLocation: order.driverLocation || undefined,
                deliveryLocation: coordinates.lat && coordinates.lng ? {
                  lat: coordinates.lat,
                  lng: coordinates.lng,
                  address: deliveryAddress.street || deliveryAddress.address || '',
                } : undefined,
              };
            });

            // Extract pagination info from response - handle different response formats
            const responseData = response.data;
            const paginationData = responseData.pagination || responseData;

            // Calculate pagination info based on what we received
            const currentPageNum = params.page || 1;
            const ordersReceived = backendOrders.length;
            const requestedLimit = params.limit || 10;

            // If we got fewer orders than requested, we're on the last page
            const hasNextPage = ordersReceived === requestedLimit;
            const hasPrevPage = currentPageNum > 1;

            // Estimate total pages based on what we know
            let totalPages = currentPageNum;
            if (hasNextPage) {
              totalPages = currentPageNum + 1; // At least one more page
            }

            // Use backend pagination if available, otherwise estimate
            const paginationInfo: PaginationInfo = {
              currentPage: currentPageNum,
              totalPages: paginationData.totalPages || totalPages,
              totalOrders: paginationData.total || paginationData.totalOrders || (hasNextPage ? (currentPageNum * requestedLimit) + 1 : (currentPageNum - 1) * requestedLimit + ordersReceived),
              hasNextPage: paginationData.hasNextPage !== undefined ? paginationData.hasNextPage : hasNextPage,
              hasPrevPage: paginationData.hasPrevPage !== undefined ? paginationData.hasPrevPage : hasPrevPage,
            };

            set({
              orders: backendOrders,
              pagination: paginationInfo,
              isLoading: false,
              error: null,
              lastFetch: now
            });
          } else {
            // If no orders or failed response, set empty array
            set({
              orders: [],
              pagination: {
                currentPage: params.page || 1,
                totalPages: 0,
                totalOrders: 0,
                hasNextPage: false,
                hasPrevPage: false,
              },
              isLoading: false,
              error: response.message || 'No orders found'
            });
          }
        } catch (error) {
          console.error('Error fetching orders:', error);
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch orders'
          });
        }
      },

      // Helper method to map backend status to frontend status
      mapBackendStatus: (backendStatus: string): Order['status'] => {
        const statusMap: Record<string, Order['status']> = {
          'pending': 'Pending',
          'confirmed': 'Confirmed',
          'preparing': 'Preparing',
          'ready': 'Ready',
          'out_for_delivery': 'On the Way',
          'delivered': 'Delivered',
          'cancelled': 'Cancelled'
        };

        return statusMap[backendStatus?.toLowerCase()] || 'Pending';
      },

      refreshOrders: async () => {
        await get().fetchOrders(true);
      },
    }),
    {
      name: 'wasel-orders-storage', // unique name for localStorage
      partialize: (state) => ({
        orders: state.orders,
        lastFetch: state.lastFetch,
      }),
    }
  )
);
